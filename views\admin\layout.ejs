<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %></title>

    <!-- CSS -->
    <link href="/css/style.css" rel="stylesheet">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Additional head content -->
    <%- typeof additionalHead !== 'undefined' ? additionalHead : '' %>
</head>
<body class="bg-gray-100">
    <div class="flex h-screen">
        <!-- Sidebar -->
        <div class="admin-sidebar">
            <!-- Logo -->
            <div class="p-6 border-b border-gray-800">
                <div class="flex items-center">
                    <i class="fas fa-atom text-blue-400 text-2xl mr-3"></i>
                    <div>
                        <h1 class="text-lg font-bold">Science Club</h1>
                        <p class="text-sm text-gray-400">Admin Panel</p>
                    </div>
                </div>
            </div>

            <!-- User Info -->
            <div class="p-6 border-b border-gray-800">
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center mr-3">
                        <i class="fas fa-user text-white"></i>
                    </div>
                    <div>
                        <p class="font-medium"><%= user.name %></p>
                        <p class="text-sm text-gray-400 capitalize"><%= user.role %></p>
                    </div>
                </div>
            </div>

            <!-- Navigation -->
            <nav class="admin-nav">
                <a href="/admin/dashboard" class="admin-nav-item <%= (typeof currentPage !== 'undefined' && currentPage === 'dashboard') ? 'active' : '' %>">
                    <i class="fas fa-tachometer-alt mr-3"></i>Dashboard
                </a>
                <a href="/admin/projects" class="admin-nav-item <%= (typeof currentPage !== 'undefined' && currentPage === 'projects') ? 'active' : '' %>">
                    <i class="fas fa-flask mr-3"></i>Projects
                </a>
                <a href="/admin/blog" class="admin-nav-item <%= (typeof currentPage !== 'undefined' && currentPage === 'blog') ? 'active' : '' %>">
                    <i class="fas fa-newspaper mr-3"></i>Blog Posts
                </a>
                <a href="/admin/resources" class="admin-nav-item <%= (typeof currentPage !== 'undefined' && currentPage === 'resources') ? 'active' : '' %>">
                    <i class="fas fa-book mr-3"></i>Resources
                </a>
                <a href="/admin/events" class="admin-nav-item <%= (typeof currentPage !== 'undefined' && currentPage === 'events') ? 'active' : '' %>">
                    <i class="fas fa-calendar mr-3"></i>Events
                </a>
                <a href="/admin/contacts" class="admin-nav-item <%= (typeof currentPage !== 'undefined' && currentPage === 'contacts') ? 'active' : '' %>">
                    <i class="fas fa-envelope mr-3"></i>Messages
                </a>
                <a href="/admin/users" class="admin-nav-item <%= (typeof currentPage !== 'undefined' && currentPage === 'users') ? 'active' : '' %>">
                    <i class="fas fa-users mr-3"></i>Users
                </a>
                <a href="/admin/settings" class="admin-nav-item <%= (typeof currentPage !== 'undefined' && currentPage === 'settings') ? 'active' : '' %>">
                    <i class="fas fa-cog mr-3"></i>Settings
                </a>
            </nav>

            <!-- Logout -->
            <div class="absolute bottom-0 left-0 right-0 p-6 border-t border-gray-800">
                <a href="/" class="admin-nav-item mb-2">
                    <i class="fas fa-external-link-alt mr-3"></i>View Site
                </a>
                <a href="/admin/logout" class="admin-nav-item text-red-400 hover:text-red-300 hover:bg-red-900">
                    <i class="fas fa-sign-out-alt mr-3"></i>Logout
                </a>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Top Bar -->
            <header class="bg-white shadow-sm border-b border-gray-200 px-6 py-4">
                <div class="flex items-center justify-between">
                    <h1 class="admin-title"><%= title %></h1>
                    <div class="flex items-center space-x-4">
                        <!-- Notifications -->
                        <div class="relative">
                            <button class="text-gray-500 hover:text-gray-700">
                                <i class="fas fa-bell text-xl"></i>
                                <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">3</span>
                            </button>
                        </div>

                        <!-- User Menu -->
                        <div class="relative">
                            <button class="flex items-center text-gray-700 hover:text-gray-900">
                                <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center mr-2">
                                    <i class="fas fa-user text-white text-sm"></i>
                                </div>
                                <span class="hidden md:block"><%= user.name %></span>
                                <i class="fas fa-chevron-down ml-2 text-sm"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Page Content -->
            <main class="flex-1 overflow-auto admin-content">
                <%- body %>
            </main>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="/js/main.js"></script>

    <!-- Additional scripts -->
    <%- typeof additionalScripts !== 'undefined' ? additionalScripts : '' %>
</body>
</html>
