const express = require('express');
const router = express.Router();

// Import models (assuming they exist)
// const Project = require('../models/Project');
// const Blog = require('../models/Blog');
// const Resource = require('../models/Resource');
// const Event = require('../models/Event');

// Search API endpoint
router.get('/api', async (req, res) => {
  try {
    const { q: query, type, limit = 10 } = req.query;
    
    if (!query || query.trim().length < 2) {
      return res.json({ results: [], total: 0 });
    }

    const searchRegex = new RegExp(query.trim(), 'i');
    const results = [];
    
    // Mock data for now - replace with actual database queries
    const mockResults = [
      {
        type: 'project',
        title: 'AI in Climate Research',
        description: 'Using artificial intelligence to predict climate patterns',
        url: '/projects/ai-climate-research',
        image: null,
        date: new Date('2024-01-15')
      },
      {
        type: 'blog',
        title: 'The Future of Quantum Computing',
        description: 'Exploring the potential of quantum computers in scientific research',
        url: '/blog/quantum-computing-future',
        image: null,
        date: new Date('2024-01-10')
      },
      {
        type: 'resource',
        title: 'Research Methodology Guide',
        description: 'A comprehensive guide to scientific research methods',
        url: '/resources/research-methodology',
        image: null,
        date: new Date('2024-01-05')
      },
      {
        type: 'event',
        title: 'Science Symposium 2024',
        description: 'Annual science symposium featuring latest research',
        url: '/events/science-symposium-2024',
        image: null,
        date: new Date('2024-02-15')
      }
    ];

    // Filter mock results based on search query
    const filteredResults = mockResults.filter(item => 
      item.title.toLowerCase().includes(query.toLowerCase()) ||
      item.description.toLowerCase().includes(query.toLowerCase())
    );

    // Filter by type if specified
    const finalResults = type ? 
      filteredResults.filter(item => item.type === type) : 
      filteredResults;

    // Limit results
    const limitedResults = finalResults.slice(0, parseInt(limit));

    res.json({
      results: limitedResults,
      total: finalResults.length,
      query: query
    });

  } catch (error) {
    console.error('Search error:', error);
    res.status(500).json({ error: 'Search failed' });
  }
});

// Search results page
router.get('/', async (req, res) => {
  try {
    const { q: query, type, page = 1 } = req.query;
    const limit = 12;
    const skip = (page - 1) * limit;

    if (!query) {
      return res.render('pages/search/index', {
        title: 'Search',
        currentPage: 'search',
        query: '',
        results: [],
        total: 0,
        currentPageNum: 1,
        totalPages: 0,
        searchTypes: ['all', 'projects', 'blog', 'resources', 'events']
      });
    }

    // Mock search results for the page
    const mockResults = [
      {
        type: 'project',
        title: 'AI in Climate Research',
        description: 'Using artificial intelligence to predict climate patterns and environmental changes',
        url: '/projects/ai-climate-research',
        image: null,
        date: new Date('2024-01-15'),
        authors: ['Dr. Sarah Johnson', 'Prof. Michael Chen']
      },
      {
        type: 'blog',
        title: 'The Future of Quantum Computing',
        description: 'Exploring the potential of quantum computers in scientific research and breakthrough discoveries',
        url: '/blog/quantum-computing-future',
        image: null,
        date: new Date('2024-01-10'),
        author: 'Dr. Emily Rodriguez'
      },
      {
        type: 'resource',
        title: 'Research Methodology Guide',
        description: 'A comprehensive guide to scientific research methods and best practices',
        url: '/resources/research-methodology',
        image: null,
        date: new Date('2024-01-05'),
        category: 'Methodology'
      },
      {
        type: 'event',
        title: 'Science Symposium 2024',
        description: 'Annual science symposium featuring latest research and networking opportunities',
        url: '/events/science-symposium-2024',
        image: null,
        date: new Date('2024-02-15'),
        location: 'University Auditorium'
      }
    ];

    // Filter results
    const filteredResults = mockResults.filter(item => 
      item.title.toLowerCase().includes(query.toLowerCase()) ||
      item.description.toLowerCase().includes(query.toLowerCase())
    );

    const finalResults = type && type !== 'all' ? 
      filteredResults.filter(item => item.type === type) : 
      filteredResults;

    const total = finalResults.length;
    const totalPages = Math.ceil(total / limit);
    const paginatedResults = finalResults.slice(skip, skip + limit);

    res.render('pages/search/index', {
      title: `Search Results for "${query}"`,
      currentPage: 'search',
      query: query,
      results: paginatedResults,
      total: total,
      currentPageNum: parseInt(page),
      totalPages: totalPages,
      searchTypes: ['all', 'projects', 'blog', 'resources', 'events'],
      selectedType: type || 'all'
    });

  } catch (error) {
    console.error('Search page error:', error);
    res.status(500).render('pages/error', { 
      title: 'Search Error', 
      error: error 
    });
  }
});

module.exports = router;
