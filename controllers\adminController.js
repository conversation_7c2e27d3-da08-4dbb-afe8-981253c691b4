const { User, Project, Blog, Resource, Event, Contact } = require('../models');
const bcrypt = require('bcrypt');

// Middleware to check if user is authenticated
exports.requireAuth = (req, res, next) => {
  if (req.session && req.session.user) {
    return next();
  } else {
    return res.redirect('/admin/login');
  }
};

// Middleware to check if user is admin
exports.requireAdmin = (req, res, next) => {
  if (req.session && req.session.user && req.session.user.role === 'admin') {
    return next();
  } else {
    return res.status(403).render('pages/error', {
      title: 'Access Denied',
      error: 'You do not have permission to access this page.'
    });
  }
};

// Get login page
exports.getLoginPage = (req, res) => {
  // Redirect to dashboard if already logged in
  if (req.session && req.session.user) {
    return res.redirect('/admin/dashboard');
  }

  res.render('admin/auth/login', {
    title: 'Admin Login',
    layout: false // Use a different layout for login page
  });
};

// Handle login
exports.login = async (req, res) => {
  try {
    const { email, password } = req.body;

    // Validate input
    if (!email || !password) {
      return res.render('admin/auth/login', {
        title: 'Admin Login',
        layout: false,
        error: 'Please provide both email and password.'
      });
    }

    // Find user by email
    const user = await User.findOne({ email: email.toLowerCase() });

    if (!user) {
      return res.render('admin/auth/login', {
        title: 'Admin Login',
        layout: false,
        error: 'Invalid email or password.'
      });
    }

    // Check password
    const isPasswordValid = await user.comparePassword(password);

    if (!isPasswordValid) {
      return res.render('admin/auth/login', {
        title: 'Admin Login',
        layout: false,
        error: 'Invalid email or password.'
      });
    }

    // Create session
    req.session.user = {
      id: user._id,
      name: user.name,
      email: user.email,
      role: user.role
    };

    res.redirect('/admin/dashboard');
  } catch (error) {
    console.error('Error during login:', error);
    res.render('admin/auth/login', {
      title: 'Admin Login',
      layout: false,
      error: 'An error occurred during login. Please try again.'
    });
  }
};

// Handle logout
exports.logout = (req, res) => {
  req.session.destroy((err) => {
    if (err) {
      console.error('Error during logout:', err);
    }
    res.redirect('/admin/login');
  });
};

// Get admin dashboard
exports.getDashboard = async (req, res) => {
  try {
    // Get statistics
    const stats = {
      projects: await Project.countDocuments(),
      blogs: await Blog.countDocuments(),
      resources: await Resource.countDocuments(),
      events: await Event.countDocuments(),
      contacts: await Contact.countDocuments(),
      unreadContacts: await Contact.countDocuments({ isRead: false })
    };

    // Get recent items
    const recentProjects = await Project.find().sort({ createdAt: -1 }).limit(5);
    const recentBlogs = await Blog.find().sort({ createdAt: -1 }).limit(5).populate('author', 'name');
    const recentContacts = await Contact.find().sort({ createdAt: -1 }).limit(5);
    const upcomingEvents = await Event.find({
      date: { $gte: new Date() },
      isPast: false
    }).sort({ date: 1 }).limit(5);

    res.render('admin/dashboard', {
      title: 'Admin Dashboard',
      user: req.session.user,
      currentPage: 'dashboard',
      stats,
      recentProjects,
      recentBlogs,
      recentContacts,
      upcomingEvents
    });
  } catch (error) {
    console.error('Error loading dashboard:', error);
    res.status(500).render('pages/error', {
      title: 'Error',
      error: 'An error occurred while loading the dashboard.'
    });
  }
};

// Get user management page
exports.getUserManagement = async (req, res) => {
  try {
    const users = await User.find().sort({ createdAt: -1 });

    res.render('admin/users/index', {
      title: 'Admin - User Management',
      users
    });
  } catch (error) {
    console.error('Error fetching users:', error);
    res.status(500).render('pages/error', {
      title: 'Error',
      error: 'An error occurred while fetching users.'
    });
  }
};

// Get form to create a new user
exports.getCreateUserForm = (req, res) => {
  res.render('admin/users/create', {
    title: 'Admin - Create New User'
  });
};

// Create a new user
exports.createUser = async (req, res) => {
  try {
    const { name, email, password, role } = req.body;

    // Validate input
    if (!name || !email || !password) {
      return res.render('admin/users/create', {
        title: 'Admin - Create New User',
        error: 'Please fill in all required fields.',
        formData: req.body
      });
    }

    // Check if user already exists
    const existingUser = await User.findOne({ email: email.toLowerCase() });
    if (existingUser) {
      return res.render('admin/users/create', {
        title: 'Admin - Create New User',
        error: 'A user with this email already exists.',
        formData: req.body
      });
    }

    // Create new user
    const newUser = new User({
      name,
      email: email.toLowerCase(),
      password,
      role: role || 'admin'
    });

    await newUser.save();

    res.redirect('/admin/users');
  } catch (error) {
    console.error('Error creating user:', error);
    res.render('admin/users/create', {
      title: 'Admin - Create New User',
      error: 'An error occurred while creating the user.',
      formData: req.body
    });
  }
};

// Delete a user
exports.deleteUser = async (req, res) => {
  try {
    const userId = req.params.id;

    // Prevent deleting own account
    if (userId === req.session.user.id) {
      return res.status(400).json({ error: 'You cannot delete your own account.' });
    }

    const user = await User.findById(userId);

    if (!user) {
      return res.status(404).json({ error: 'User not found.' });
    }

    await User.findByIdAndDelete(userId);

    res.json({ success: true });
  } catch (error) {
    console.error('Error deleting user:', error);
    res.status(500).json({ error: 'An error occurred while deleting the user.' });
  }
};

// Get settings page
exports.getSettings = (req, res) => {
  res.render('admin/settings', {
    title: 'Admin - Settings',
    user: req.session.user
  });
};

// Update user profile
exports.updateProfile = async (req, res) => {
  try {
    const { name, email, currentPassword, newPassword } = req.body;
    const userId = req.session.user.id;

    const user = await User.findById(userId);

    if (!user) {
      return res.render('admin/settings', {
        title: 'Admin - Settings',
        user: req.session.user,
        error: 'User not found.'
      });
    }

    // Update name and email
    user.name = name;
    user.email = email.toLowerCase();

    // Update password if provided
    if (newPassword) {
      if (!currentPassword) {
        return res.render('admin/settings', {
          title: 'Admin - Settings',
          user: req.session.user,
          error: 'Current password is required to set a new password.'
        });
      }

      const isCurrentPasswordValid = await user.comparePassword(currentPassword);
      if (!isCurrentPasswordValid) {
        return res.render('admin/settings', {
          title: 'Admin - Settings',
          user: req.session.user,
          error: 'Current password is incorrect.'
        });
      }

      user.password = newPassword;
    }

    await user.save();

    // Update session
    req.session.user.name = user.name;
    req.session.user.email = user.email;

    res.render('admin/settings', {
      title: 'Admin - Settings',
      user: req.session.user,
      success: 'Profile updated successfully.'
    });
  } catch (error) {
    console.error('Error updating profile:', error);
    res.render('admin/settings', {
      title: 'Admin - Settings',
      user: req.session.user,
      error: 'An error occurred while updating your profile.'
    });
  }
};
