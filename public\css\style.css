/* Science Club Website Styles */

/* CSS Variables for Design System */
:root {
  /* Primary Colors */
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-200: #bfdbfe;
  --primary-300: #93c5fd;
  --primary-400: #60a5fa;
  --primary-500: #3b82f6;
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;
  --primary-800: #1e40af;
  --primary-900: #1e3a8a;

  /* Secondary Colors */
  --secondary-50: #f0fdf4;
  --secondary-100: #dcfce7;
  --secondary-200: #bbf7d0;
  --secondary-300: #86efac;
  --secondary-400: #4ade80;
  --secondary-500: #22c55e;
  --secondary-600: #16a34a;
  --secondary-700: #15803d;
  --secondary-800: #166534;
  --secondary-900: #14532d;

  /* Accent Colors */
  --accent-50: #fdf4ff;
  --accent-100: #fae8ff;
  --accent-200: #f5d0fe;
  --accent-300: #f0abfc;
  --accent-400: #e879f9;
  --accent-500: #d946ef;
  --accent-600: #c026d3;
  --accent-700: #a21caf;
  --accent-800: #86198f;
  --accent-900: #701a75;

  /* Neutral Colors */
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;

  /* Semantic Colors */
  --success-500: #10b981;
  --warning-500: #f59e0b;
  --error-500: #ef4444;
  --info-500: #3b82f6;

  /* Spacing Scale */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;
  --space-16: 4rem;
  --space-20: 5rem;
  --space-24: 6rem;

  /* Border Radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-3xl: 1.5rem;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

  /* Typography */
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  --font-size-5xl: 3rem;
  --font-size-6xl: 3.75rem;

  /* Line Heights */
  --leading-tight: 1.25;
  --leading-snug: 1.375;
  --leading-normal: 1.5;
  --leading-relaxed: 1.625;
  --leading-loose: 2;

  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
}

/* CSS Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: var(--leading-normal);
  color: var(--gray-700);
  background-color: var(--gray-50);
  font-size: var(--font-size-base);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Container and Layout */
.max-w-7xl { max-width: 1280px; margin: 0 auto; }
.max-w-4xl { max-width: 896px; margin: 0 auto; }
.max-w-3xl { max-width: 768px; margin: 0 auto; }
.max-w-2xl { max-width: 672px; margin: 0 auto; }
.max-w-md { max-width: 448px; margin: 0 auto; }

/* Spacing */
.px-4 { padding-left: 16px; padding-right: 16px; }
.px-6 { padding-left: 24px; padding-right: 24px; }
.px-8 { padding-left: 32px; padding-right: 32px; }
.py-4 { padding-top: 16px; padding-bottom: 16px; }
.py-6 { padding-top: 24px; padding-bottom: 24px; }
.py-8 { padding-top: 32px; padding-bottom: 32px; }
.py-12 { padding-top: 48px; padding-bottom: 48px; }
.py-16 { padding-top: 64px; padding-bottom: 64px; }
.py-20 { padding-top: 80px; padding-bottom: 80px; }

.mb-2 { margin-bottom: 8px; }
.mb-4 { margin-bottom: 16px; }
.mb-6 { margin-bottom: 24px; }
.mb-8 { margin-bottom: 32px; }
.mb-12 { margin-bottom: 48px; }
.mt-4 { margin-top: 16px; }
.mt-6 { margin-top: 24px; }
.mt-8 { margin-top: 32px; }
.mt-12 { margin-top: 48px; }

/* Grid System */
.grid { display: grid; }
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
.gap-4 { gap: 16px; }
.gap-6 { gap: 24px; }
.gap-8 { gap: 32px; }

/* Responsive Grid */
@media (min-width: 768px) {
  .md\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .md\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .md\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
  .md\:flex-row { flex-direction: row; }
  .md\:text-2xl { font-size: 24px; }
  .md\:text-4xl { font-size: 36px; }
  .md\:text-6xl { font-size: 60px; }
  .md\:block { display: block; }
  .md\:flex { display: flex; }
  .md\:hidden { display: none; }
}

@media (min-width: 1024px) {
  .lg\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .lg\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
  .lg\:px-8 { padding-left: 32px; padding-right: 32px; }
  .lg\:block { display: block; }
}

@media (min-width: 640px) {
  .sm\:flex-row { flex-direction: row; }
  .sm\:px-6 { padding-left: 24px; padding-right: 24px; }
}

/* Flexbox */
.flex { display: flex; }
.flex-col { flex-direction: column; }
.flex-wrap { flex-wrap: wrap; }
.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.space-x-4 > * + * { margin-left: 16px; }
.space-x-6 > * + * { margin-left: 24px; }
.space-y-3 > * + * { margin-top: 12px; }
.space-y-4 > * + * { margin-top: 16px; }
.space-y-6 > * + * { margin-top: 24px; }

/* Text Styles */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }
.text-xs { font-size: 12px; }
.text-sm { font-size: 14px; }
.text-base { font-size: 16px; }
.text-lg { font-size: 18px; }
.text-xl { font-size: 20px; }
.text-2xl { font-size: 24px; }
.text-3xl { font-size: 30px; }
.text-4xl { font-size: 36px; }
.text-5xl { font-size: 48px; }
.text-6xl { font-size: 60px; }

.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }

/* Colors */
.text-white { color: white; }
.text-gray-400 { color: #9ca3af; }
.text-gray-500 { color: #6b7280; }
.text-gray-600 { color: #4b5563; }
.text-gray-700 { color: #374151; }
.text-gray-900 { color: #111827; }
.text-blue-600 { color: #2563eb; }
.text-blue-800 { color: #1e40af; }
.text-green-600 { color: #059669; }
.text-green-800 { color: #065f46; }
.text-red-600 { color: #dc2626; }
.text-red-800 { color: #991b1b; }
.text-yellow-800 { color: #92400e; }
.text-purple-800 { color: #6b21a8; }

/* Background Colors */
.bg-white { background-color: white; }
.bg-gray-50 { background-color: #f9fafb; }
.bg-gray-100 { background-color: #f3f4f6; }
.bg-gray-200 { background-color: #e5e7eb; }
.bg-gray-900 { background-color: #111827; }
.bg-blue-50 { background-color: #eff6ff; }
.bg-blue-100 { background-color: #dbeafe; }
.bg-blue-600 { background-color: #2563eb; }
.bg-green-100 { background-color: #dcfce7; }
.bg-green-600 { background-color: #16a34a; }
.bg-yellow-100 { background-color: #fef3c7; }
.bg-red-100 { background-color: #fee2e2; }
.bg-purple-100 { background-color: #f3e8ff; }

/* Gradients */
.bg-gradient-to-r { background-image: linear-gradient(to right, var(--tw-gradient-stops)); }
.from-blue-600 { --tw-gradient-from: #2563eb; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(37, 99, 235, 0)); }
.to-purple-700 { --tw-gradient-to: #7c3aed; }
.to-blue-800 { --tw-gradient-to: #1e40af; }
.from-green-600 { --tw-gradient-from: #059669; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(5, 150, 105, 0)); }
.to-blue-700 { --tw-gradient-to: #1d4ed8; }
.from-purple-600 { --tw-gradient-from: #9333ea; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(147, 51, 234, 0)); }
.from-green-500 { --tw-gradient-from: #10b981; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(16, 185, 129, 0)); }
.to-blue-600 { --tw-gradient-to: #2563eb; }
.from-blue-500 { --tw-gradient-from: #3b82f6; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(59, 130, 246, 0)); }

/* Border and Rounded */
.border { border: 1px solid #e5e7eb; }
.border-b { border-bottom: 1px solid #e5e7eb; }
.border-t { border-top: 1px solid #e5e7eb; }
.border-gray-200 { border-color: #e5e7eb; }
.rounded { border-radius: 4px; }
.rounded-lg { border-radius: 8px; }
.rounded-full { border-radius: 9999px; }

/* Shadow */
.shadow-sm { box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05); }
.shadow-md { box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); }
.shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }

/* Width and Height */
.w-full { width: 100%; }
.w-32 { width: 128px; }
.w-48 { width: 192px; }
.h-48 { height: 192px; }
.h-64 { height: 256px; }
.h-96 { height: 384px; }
.min-h-screen { min-height: 100vh; }

/* Object Fit */
.object-cover { object-fit: cover; }

/* Overflow */
.overflow-hidden { overflow: hidden; }

/* Position */
.relative { position: relative; }
.absolute { position: absolute; }
.sticky { position: sticky; }
.top-0 { top: 0; }

/* Z-index */
.z-50 { z-index: 50; }

/* Opacity */
.opacity-90 { opacity: 0.9; }

/* Transitions */
.transition-colors { transition: color 0.2s, background-color 0.2s; }
.transition-shadow { transition: box-shadow 0.3s; }
.transition-all { transition: all 0.2s; }
.duration-200 { transition-duration: 0.2s; }
.duration-300 { transition-duration: 0.3s; }

/* Hover Effects */
.hover\:bg-blue-700:hover { background-color: #1d4ed8; }
.hover\:bg-gray-100:hover { background-color: #f3f4f6; }
.hover\:text-blue-600:hover { color: #2563eb; }
.hover\:text-blue-800:hover { color: #1e40af; }
.hover\:shadow-lg:hover { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }

/* Focus Effects */
.focus\:outline-none:focus { outline: none; }
.focus\:ring-2:focus { box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1); }

/* Display */
.block { display: block; }
.inline-block { display: inline-block; }
.hidden { display: none; }

/* Button Styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  text-decoration: none;
  border: none;
  cursor: pointer;
  transition: all var(--transition-normal);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-sm);
  line-height: var(--leading-tight);
  position: relative;
  overflow: hidden;
}

.btn:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Button Sizes */
.btn-sm {
  padding: var(--space-2) var(--space-3);
  font-size: var(--font-size-xs);
}

.btn-md {
  padding: var(--space-3) var(--space-4);
  font-size: var(--font-size-sm);
}

.btn-lg {
  padding: var(--space-4) var(--space-6);
  font-size: var(--font-size-base);
}

.btn-xl {
  padding: var(--space-5) var(--space-8);
  font-size: var(--font-size-lg);
}

/* Primary Button */
.btn-primary {
  background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
  color: white;
  box-shadow: var(--shadow-sm);
  padding: var(--space-3) var(--space-4);
}

.btn-primary:hover {
  background: linear-gradient(135deg, var(--primary-700), var(--primary-800));
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
  text-decoration: none;
  color: white;
}

.btn-primary:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

/* Secondary Button */
.btn-secondary {
  background: linear-gradient(135deg, var(--gray-600), var(--gray-700));
  color: white;
  box-shadow: var(--shadow-sm);
  padding: var(--space-3) var(--space-4);
}

.btn-secondary:hover {
  background: linear-gradient(135deg, var(--gray-700), var(--gray-800));
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
  text-decoration: none;
  color: white;
}

/* Success Button */
.btn-success {
  background: linear-gradient(135deg, var(--secondary-600), var(--secondary-700));
  color: white;
  box-shadow: var(--shadow-sm);
  padding: var(--space-3) var(--space-4);
}

.btn-success:hover {
  background: linear-gradient(135deg, var(--secondary-700), var(--secondary-800));
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
  text-decoration: none;
  color: white;
}

/* Danger Button */
.btn-danger {
  background: linear-gradient(135deg, var(--error-500), #dc2626);
  color: white;
  box-shadow: var(--shadow-sm);
  padding: var(--space-3) var(--space-4);
}

.btn-danger:hover {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
  text-decoration: none;
  color: white;
}

/* Outline Button */
.btn-outline {
  border: 2px solid var(--primary-600);
  color: var(--primary-600);
  background-color: transparent;
  padding: calc(var(--space-3) - 2px) calc(var(--space-4) - 2px);
  position: relative;
  overflow: hidden;
}

.btn-outline::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
  transition: left var(--transition-normal);
  z-index: -1;
}

.btn-outline:hover {
  color: white !important;
  text-decoration: none;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
  border-color: var(--primary-600);
}

.btn-outline:hover::before {
  left: 0;
}

/* Ghost Button */
.btn-ghost {
  background-color: transparent;
  color: var(--gray-600);
  padding: var(--space-3) var(--space-4);
}

.btn-ghost:hover {
  background-color: var(--gray-100);
  color: var(--gray-700);
  text-decoration: none;
}

/* Button Combinations - Enhanced hover states */
.btn.btn-primary {
  background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
  color: white;
  box-shadow: var(--shadow-sm);
}

.btn.btn-primary:hover {
  background: linear-gradient(135deg, var(--primary-700), var(--primary-800));
  color: white !important;
  text-decoration: none;
}

.btn.btn-secondary {
  background: linear-gradient(135deg, var(--gray-600), var(--gray-700));
  color: white;
  box-shadow: var(--shadow-sm);
}

.btn.btn-secondary:hover {
  background: linear-gradient(135deg, var(--gray-700), var(--gray-800));
  color: white !important;
  text-decoration: none;
}

.btn.btn-success {
  background: linear-gradient(135deg, var(--secondary-600), var(--secondary-700));
  color: white;
  box-shadow: var(--shadow-sm);
}

.btn.btn-success:hover {
  background: linear-gradient(135deg, var(--secondary-700), var(--secondary-800));
  color: white !important;
  text-decoration: none;
}

.btn.btn-danger {
  background: linear-gradient(135deg, var(--error-500), #dc2626);
  color: white;
  box-shadow: var(--shadow-sm);
}

.btn.btn-danger:hover {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  color: white !important;
  text-decoration: none;
}

.btn.btn-outline {
  border: 2px solid var(--primary-600);
  color: var(--primary-600);
  background-color: transparent;
}

/* White border outline buttons for dark backgrounds */
.btn.btn-outline.border-white {
  border-color: white;
  color: white;
}

.btn.btn-outline.border-white:hover {
  background-color: white;
  color: var(--primary-600) !important;
  border-color: white;
}

.btn.btn-outline.border-white:hover::before {
  background: white;
}

/* Ensure proper spacing for icons in buttons */
.btn i {
  display: inline-flex;
  align-items: center;
}

/* Full width buttons */
.btn.w-full {
  width: 100%;
  justify-content: center;
}

/* Newsletter Form Enhancements */
.newsletter-input {
  transition: all 0.3s ease;
  position: relative;
}

.newsletter-input:focus {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.email-suggestion {
  animation: slideInUp 0.3s ease;
}

.email-error {
  animation: shake 0.5s ease;
}

.newsletter-success {
  animation: fadeInUp 0.5s ease;
}

/* Newsletter form specific styling */
form[data-newsletter] {
  max-width: 400px;
}

form[data-newsletter] input[type="email"] {
  min-width: 200px;
  flex: 1;
}

form[data-newsletter] button {
  flex-shrink: 0;
  min-width: auto;
}

/* Responsive newsletter forms */
@media (max-width: 640px) {
  form[data-newsletter] {
    max-width: 100%;
  }

  form[data-newsletter] .flex-col {
    gap: 12px;
  }

  form[data-newsletter] button {
    width: 100%;
    justify-content: center;
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Enhanced input focus states */
input[type="email"]:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Floating placeholder effect */
.floating-placeholder {
  position: relative;
}

.floating-placeholder input:focus + label,
.floating-placeholder input:not(:placeholder-shown) + label {
  transform: translateY(-1.5rem) scale(0.8);
  color: var(--primary-600);
}

/* Form Styles */
.form-group {
  margin-bottom: 16px;
}

.form-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 8px;
}

.form-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  font-size: 14px;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.form-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  font-size: 14px;
  resize: vertical;
  min-height: 80px;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.form-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  font-size: 14px;
  background-color: white;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.form-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-checkbox {
  height: 16px;
  width: 16px;
  color: #2563eb;
  border: 1px solid #d1d5db;
  border-radius: 4px;
}

/* Card Styles */
.card {
  background-color: white;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: all var(--transition-normal);
  border: 1px solid var(--gray-200);
  position: relative;
}

.card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-500), var(--accent-500));
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.card:hover::before {
  opacity: 1;
}

.card-header {
  padding: var(--space-6);
  border-bottom: 1px solid var(--gray-200);
  background: linear-gradient(135deg, var(--gray-50), white);
}

.card-body {
  padding: var(--space-6);
}

.card-footer {
  padding: var(--space-6);
  border-top: 1px solid var(--gray-200);
  background: linear-gradient(135deg, white, var(--gray-50));
}

/* Enhanced Card Variants */
.card-elevated {
  box-shadow: var(--shadow-lg);
  border: none;
}

.card-elevated:hover {
  box-shadow: var(--shadow-xl);
  transform: translateY(-4px);
}

.card-interactive {
  cursor: pointer;
  transition: all var(--transition-normal);
}

.card-interactive:hover {
  box-shadow: var(--shadow-xl);
  transform: translateY(-3px) scale(1.02);
}

.card-gradient {
  background: linear-gradient(135deg, white, var(--primary-50));
  border: 1px solid var(--primary-200);
}

.card-glass {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Alert Styles */
.alert {
  padding: 16px;
  border-radius: 6px;
  margin-bottom: 16px;
}

.alert-success {
  background-color: #dcfce7;
  border: 1px solid #86efac;
  color: #15803d;
}

.alert-error {
  background-color: #fee2e2;
  border: 1px solid #fca5a5;
  color: #dc2626;
}

.alert-warning {
  background-color: #fef3c7;
  border: 1px solid #fcd34d;
  color: #d97706;
}

.alert-info {
  background-color: #dbeafe;
  border: 1px solid #93c5fd;
  color: #2563eb;
}

/* Navigation Styles */
.navbar {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  box-shadow: var(--shadow-sm);
  border-bottom: 1px solid var(--gray-200);
  transition: all var(--transition-normal);
}

.navbar.scrolled {
  background: rgba(255, 255, 255, 0.98);
  box-shadow: var(--shadow-lg);
}

/* Modern Animated Hamburger Menu */
.mobile-menu-toggle {
  z-index: 1001;
  transition: all var(--transition-normal);
}

.hamburger-line {
  display: block;
  width: 24px;
  height: 2px;
  background-color: var(--gray-700);
  margin: 4px 0;
  transition: all var(--transition-normal);
  transform-origin: center;
}

.mobile-menu-toggle.active .hamburger-line:nth-child(1) {
  transform: rotate(45deg) translate(6px, 6px);
}

.mobile-menu-toggle.active .hamburger-line:nth-child(2) {
  opacity: 0;
  transform: scale(0);
}

.mobile-menu-toggle.active .hamburger-line:nth-child(3) {
  transform: rotate(-45deg) translate(6px, -6px);
}

/* Mobile Menu Overlay */
.mobile-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  visibility: hidden;
  opacity: 0;
  transition: all var(--transition-normal);
}

.mobile-menu-overlay.active {
  visibility: visible;
  opacity: 1;
}

.mobile-menu-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  transition: all var(--transition-normal);
}

.mobile-menu-panel {
  position: absolute;
  top: 0;
  right: 0;
  width: 320px;
  max-width: 85vw;
  height: 100vh;
  background: white;
  box-shadow: var(--shadow-xl);
  transform: translateX(100%);
  transition: transform var(--transition-normal);
  display: flex;
  flex-direction: column;
}

.mobile-menu-overlay.active .mobile-menu-panel {
  transform: translateX(0);
}

.mobile-menu-header {
  border-bottom: 1px solid var(--gray-200);
  background: linear-gradient(135deg, var(--primary-50), white);
}

.mobile-menu-close {
  padding: 8px;
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
}

.mobile-menu-close:hover {
  background-color: var(--gray-100);
  color: var(--primary-600);
}

.mobile-menu-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.mobile-nav-links {
  flex: 1;
  padding: 16px 0;
}

.mobile-nav-link {
  display: flex;
  align-items: center;
  padding: 16px 24px;
  color: var(--gray-700);
  text-decoration: none;
  transition: all var(--transition-fast);
  border-left: 4px solid transparent;
}

.mobile-nav-link:hover {
  background-color: var(--primary-50);
  color: var(--primary-600);
  border-left-color: var(--primary-600);
  text-decoration: none;
}

.mobile-nav-link.active {
  background-color: var(--primary-100);
  color: var(--primary-700);
  border-left-color: var(--primary-600);
  font-weight: 600;
}

.mobile-nav-link i:first-child {
  width: 20px;
  margin-right: 16px;
  text-align: center;
}

.mobile-nav-link span {
  flex: 1;
}

.mobile-nav-link i:last-child {
  opacity: 0.5;
  transition: all var(--transition-fast);
}

.mobile-nav-link:hover i:last-child {
  opacity: 1;
  transform: translateX(4px);
}

.navbar-brand {
  font-size: var(--font-size-2xl);
  font-weight: 700;
  background: linear-gradient(135deg, var(--primary-600), var(--accent-600));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-decoration: none;
  transition: all var(--transition-normal);
}

.navbar-brand:hover {
  text-decoration: none;
  transform: scale(1.05);
}

.navbar-nav {
  display: flex;
  align-items: center;
  gap: var(--space-8);
}

.nav-link {
  color: var(--gray-700);
  text-decoration: none;
  font-weight: 500;
  transition: all var(--transition-normal);
  position: relative;
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius-lg);
}

.nav-link::before {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 50%;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--primary-500), var(--accent-500));
  transition: all var(--transition-normal);
  transform: translateX(-50%);
}

.nav-link:hover {
  color: var(--primary-600);
  text-decoration: none;
  background-color: var(--primary-50);
}

.nav-link:hover::before {
  width: 80%;
}

.nav-link.active {
  color: var(--primary-600);
  background-color: var(--primary-50);
  border: 1px solid var(--primary-200);
}

.nav-link.active::before {
  width: 80%;
}

/* Hero Section */
.hero {
  background: linear-gradient(135deg, var(--primary-600), var(--primary-800), var(--accent-600));
  color: white;
  position: relative;
  overflow: hidden;
}

.hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
}

.hero-content {
  position: relative;
  z-index: 1;
}

.hero-title {
  font-size: var(--font-size-4xl);
  font-weight: 700;
  margin-bottom: var(--space-4);
  line-height: var(--leading-tight);
  background: linear-gradient(135deg, white, rgba(255, 255, 255, 0.8));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-subtitle {
  font-size: var(--font-size-xl);
  margin-bottom: var(--space-8);
  opacity: 0.9;
  line-height: var(--leading-relaxed);
}

.hero-cta {
  display: flex;
  gap: var(--space-4);
  flex-wrap: wrap;
  justify-content: center;
}

@media (min-width: 768px) {
  .hero-title {
    font-size: var(--font-size-6xl);
  }
  .hero-subtitle {
    font-size: var(--font-size-2xl);
  }
  .hero-cta {
    justify-content: flex-start;
  }
}

/* Section Styles */
.section {
  padding-top: 64px;
  padding-bottom: 64px;
}

.section-title {
  font-size: 30px;
  font-weight: 700;
  text-align: center;
  margin-bottom: 48px;
  color: #111827;
}

.section-subtitle {
  font-size: 18px;
  color: #6b7280;
  text-align: center;
  margin-bottom: 48px;
  max-width: 768px;
  margin-left: auto;
  margin-right: auto;
}

@media (min-width: 768px) {
  .section-title {
    font-size: 36px;
  }
}

/* Project Card Styles */
.project-card {
  background-color: white;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: all var(--transition-normal);
  border: 1px solid var(--gray-200);
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.project-card:hover {
  box-shadow: var(--shadow-xl);
  transform: translateY(-4px);
}

.project-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-500), var(--secondary-500));
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.project-card:hover::before {
  opacity: 1;
}

.project-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
  transition: transform var(--transition-normal);
}

.project-card:hover .project-image {
  transform: scale(1.05);
}

.project-content {
  padding: var(--space-6);
  flex: 1;
  display: flex;
  flex-direction: column;
}

.project-title {
  font-size: var(--font-size-xl);
  font-weight: 600;
  margin-bottom: var(--space-2);
  color: var(--gray-900);
  line-height: var(--leading-tight);
}

.project-description {
  color: var(--gray-600);
  margin-bottom: var(--space-4);
  line-height: var(--leading-relaxed);
  flex: 1;
}

.project-authors {
  font-size: var(--font-size-sm);
  color: var(--primary-600);
  margin-bottom: var(--space-3);
  font-weight: 500;
}

.project-tags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-2);
  margin-bottom: var(--space-4);
}

.project-tag {
  background: linear-gradient(135deg, var(--primary-100), var(--primary-200));
  color: var(--primary-700);
  font-size: var(--font-size-xs);
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-3xl);
  font-weight: 500;
  border: 1px solid var(--primary-300);
  transition: all var(--transition-fast);
}

.project-tag:hover {
  background: linear-gradient(135deg, var(--primary-200), var(--primary-300));
  transform: translateY(-1px);
}

/* Blog Card Styles */
.blog-card {
  background-color: white;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: all var(--transition-normal);
  border: 1px solid var(--gray-200);
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.blog-card:hover {
  box-shadow: var(--shadow-xl);
  transform: translateY(-4px);
}

.blog-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--accent-500), var(--secondary-500));
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.blog-card:hover::before {
  opacity: 1;
}

.blog-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
  transition: transform var(--transition-normal);
}

.blog-card:hover .blog-image {
  transform: scale(1.05);
}

.blog-content {
  padding: var(--space-6);
  flex: 1;
  display: flex;
  flex-direction: column;
}

.blog-title {
  font-size: var(--font-size-xl);
  font-weight: 600;
  margin-bottom: var(--space-2);
  color: var(--gray-900);
  line-height: var(--leading-tight);
}

.blog-excerpt {
  color: var(--gray-600);
  margin-bottom: var(--space-4);
  line-height: var(--leading-relaxed);
  flex: 1;
}

.blog-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: var(--font-size-sm);
  color: var(--gray-500);
  padding-top: var(--space-4);
  border-top: 1px solid var(--gray-200);
}

.blog-author {
  font-weight: 500;
  color: var(--primary-600);
}

.blog-date {
  display: flex;
  align-items: center;
  gap: var(--space-1);
}

.blog-categories {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-2);
  margin-bottom: var(--space-4);
}

.blog-category {
  background: linear-gradient(135deg, var(--secondary-100), var(--secondary-200));
  color: var(--secondary-700);
  font-size: var(--font-size-xs);
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-3xl);
  font-weight: 500;
  border: 1px solid var(--secondary-300);
  transition: all var(--transition-fast);
}

.blog-category:hover {
  background: linear-gradient(135deg, var(--secondary-200), var(--secondary-300));
  transform: translateY(-1px);
}

/* Event Card Styles */
.event-card {
  background-color: white;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: all var(--transition-normal);
  border: 1px solid var(--gray-200);
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.event-card:hover {
  box-shadow: var(--shadow-xl);
  transform: translateY(-4px);
}

.event-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--secondary-500), var(--accent-500));
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.event-card:hover::before {
  opacity: 1;
}

.event-date {
  background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
  color: white;
  padding: var(--space-4);
  text-align: center;
  position: relative;
}

.event-date::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-top: 10px solid var(--primary-700);
}

.event-day {
  font-size: var(--font-size-3xl);
  font-weight: 700;
  line-height: var(--leading-tight);
}

.event-month {
  font-size: var(--font-size-sm);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  opacity: 0.9;
}

.event-content {
  padding: var(--space-6);
  flex: 1;
  display: flex;
  flex-direction: column;
}

.event-title {
  font-size: var(--font-size-xl);
  font-weight: 600;
  margin-bottom: var(--space-2);
  color: var(--gray-900);
  line-height: var(--leading-tight);
}

.event-description {
  color: var(--gray-600);
  margin-bottom: var(--space-4);
  line-height: var(--leading-relaxed);
  flex: 1;
}

.event-location {
  font-size: var(--font-size-sm);
  color: var(--primary-600);
  display: flex;
  align-items: center;
  gap: var(--space-1);
  font-weight: 500;
  padding: var(--space-2) var(--space-3);
  background-color: var(--primary-50);
  border-radius: var(--radius-lg);
  border: 1px solid var(--primary-200);
}

/* Resource Card Styles */
.resource-card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  padding: 24px;
  transition: box-shadow 0.3s;
}

.resource-card:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.resource-icon {
  width: 48px;
  height: 48px;
  margin-bottom: 16px;
}

.resource-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #111827;
}

.resource-description {
  color: #6b7280;
  margin-bottom: 16px;
}

.resource-type {
  display: inline-block;
  background-color: #f3e8ff;
  color: #6b21a8;
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 9999px;
  text-transform: uppercase;
}

/* Footer Styles */
.footer {
  background-color: #111827;
  color: white;
}

.footer-content {
  padding-top: 48px;
  padding-bottom: 48px;
}

.footer-section {
  margin-bottom: 32px;
}

.footer-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
}

.footer-link {
  color: #d1d5db;
  text-decoration: none;
  transition: color 0.2s;
}

.footer-link:hover {
  color: white;
  text-decoration: none;
}

.footer-bottom {
  border-top: 1px solid #374151;
  padding-top: 24px;
  padding-bottom: 24px;
  text-align: center;
  color: #9ca3af;
}

/* Admin Styles */
.admin-sidebar {
  background-color: #111827;
  color: white;
  width: 256px;
  min-height: 100vh;
}

.admin-nav {
  padding-top: 16px;
  padding-bottom: 16px;
}

.admin-nav-item {
  display: block;
  padding: 12px 24px;
  color: #d1d5db;
  text-decoration: none;
  transition: all 0.2s;
}

.admin-nav-item:hover {
  background-color: #374151;
  color: white;
  text-decoration: none;
}

.admin-nav-item.active {
  background-color: #2563eb;
  color: white;
}

.admin-content {
  flex: 1;
  padding: 24px;
}

.admin-header {
  margin-bottom: 24px;
}

.admin-title {
  font-size: 24px;
  font-weight: 700;
  color: #111827;
}

/* Table Styles */
.table {
  width: 100%;
  border-collapse: collapse;
}

.table th {
  background-color: #f9fafb;
  padding: 12px 24px;
  text-align: left;
  font-size: 12px;
  font-weight: 500;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border-bottom: 1px solid #e5e7eb;
}

.table td {
  padding: 16px 24px;
  white-space: nowrap;
  font-size: 14px;
  color: #111827;
  border-bottom: 1px solid #e5e7eb;
}

.table tbody tr:hover {
  background-color: #f9fafb;
}

/* Utility Classes */
.flex-1 { flex: 1; }
.flex-shrink-0 { flex-shrink: 0; }
.whitespace-nowrap { white-space: nowrap; }
.uppercase { text-transform: uppercase; }
.lowercase { text-transform: lowercase; }
.capitalize { text-transform: capitalize; }
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Enhanced Responsive Design */
@media (max-width: 480px) {
  .hero-title {
    font-size: 24px;
    line-height: 1.2;
  }
  .hero-subtitle {
    font-size: 14px;
    line-height: 1.4;
  }
  .section-title {
    font-size: 20px;
  }
  .btn {
    padding: 12px 16px;
    font-size: 14px;
  }
  .btn-lg {
    padding: 14px 20px;
    font-size: 16px;
  }
  .px-4 {
    padding-left: 16px;
    padding-right: 16px;
  }
  .py-16 {
    padding-top: 32px;
    padding-bottom: 32px;
  }
  .py-12 {
    padding-top: 24px;
    padding-bottom: 24px;
  }
}

@media (max-width: 767px) {
  .hero-title {
    font-size: 28px;
    line-height: 1.3;
  }
  .hero-subtitle {
    font-size: 16px;
    line-height: 1.5;
  }
  .section-title {
    font-size: 24px;
  }
  .navbar-nav {
    flex-direction: column;
    gap: 8px;
  }
  .grid-3 {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  .hero-cta {
    flex-direction: column;
    gap: 12px;
  }
  .hero-cta .btn {
    width: 100%;
    justify-content: center;
  }
}

@media (min-width: 768px) and (max-width: 1023px) {
  .hero-title {
    font-size: 36px;
  }
  .hero-subtitle {
    font-size: 18px;
  }
  .section-title {
    font-size: 28px;
  }
}















/* Enhanced Mobile Responsiveness */
@media (max-width: 640px) {
  .mobile-menu-panel {
    width: 100vw;
    max-width: 100vw;
  }

  .hero-cta {
    padding: 0 16px;
  }

  .section-title {
    text-align: center;
  }

  .btn-lg {
    padding: 16px 24px;
    font-size: 16px;
  }

  .form-input {
    font-size: 16px; /* Prevents zoom on iOS */
  }
}

/* Print Styles */
@media print {
  .navbar,
  .mobile-menu-overlay,
  .btn,
  .footer {
    display: none !important;
  }

  body {
    font-size: 12pt;
    line-height: 1.4;
  }

  .hero {
    background: none !important;
    color: black !important;
  }
}

/* Loading spinner */
.spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid #d1d5db;
  border-top: 2px solid #2563eb;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Custom scrollbar */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Additional grid utilities */
.grid-1 { display: grid; grid-template-columns: repeat(1, minmax(0, 1fr)); gap: 24px; }
.grid-2 { display: grid; grid-template-columns: repeat(2, minmax(0, 1fr)); gap: 24px; }
.grid-3 { display: grid; grid-template-columns: repeat(1, minmax(0, 1fr)); gap: 24px; }
.grid-4 { display: grid; grid-template-columns: repeat(4, minmax(0, 1fr)); gap: 24px; }

/* Responsive grid utilities */
@media (min-width: 768px) {
  .grid-3 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
}

@media (min-width: 1024px) {
  .grid-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
}

/* Additional spacing */
.p-2 { padding: 8px; }
.p-3 { padding: 12px; }
.p-4 { padding: 16px; }
.p-6 { padding: 24px; }
.p-8 { padding: 32px; }
.p-12 { padding: 48px; }

.m-2 { margin: 8px; }
.m-3 { margin: 12px; }
.m-4 { margin: 16px; }
.m-6 { margin: 24px; }
.m-8 { margin: 32px; }
.m-12 { margin: 48px; }

.mr-1 { margin-right: 4px; }
.mr-2 { margin-right: 8px; }
.mr-3 { margin-right: 12px; }
.mr-4 { margin-right: 16px; }
.ml-1 { margin-left: 4px; }
.ml-2 { margin-left: 8px; }
.ml-3 { margin-left: 12px; }
.ml-4 { margin-left: 16px; }

/* Width utilities */
.w-8 { width: 32px; }
.w-10 { width: 40px; }
.w-12 { width: 48px; }
.w-16 { width: 64px; }
.w-20 { width: 80px; }
.w-24 { width: 96px; }
.w-64 { width: 256px; }

/* Height utilities */
.h-8 { height: 32px; }
.h-10 { height: 40px; }
.h-12 { height: 48px; }
.h-16 { height: 64px; }
.h-20 { height: 80px; }
.h-24 { height: 96px; }
.h-32 { height: 128px; }

/* Additional colors */
.text-blue-300 { color: #93c5fd; }
.text-blue-400 { color: #60a5fa; }
.text-blue-500 { color: #3b82f6; }
.text-blue-700 { color: #1d4ed8; }
.text-blue-900 { color: #1e3a8a; }

.bg-blue-300 { background-color: #93c5fd; }
.bg-blue-400 { background-color: #60a5fa; }
.bg-blue-500 { background-color: #3b82f6; }
.bg-blue-700 { background-color: #1d4ed8; }
.bg-blue-800 { background-color: #1e40af; }
.bg-blue-900 { background-color: #1e3a8a; }

.bg-green-50 { background-color: #f0fdf4; }
.bg-green-500 { background-color: #22c55e; }
.bg-green-700 { background-color: #15803d; }

.bg-red-50 { background-color: #fef2f2; }
.bg-red-500 { background-color: #ef4444; }
.bg-red-600 { background-color: #dc2626; }

.bg-yellow-50 { background-color: #fefce8; }
.bg-yellow-400 { background-color: #facc15; }
.bg-yellow-500 { background-color: #eab308; }

.bg-purple-50 { background-color: #faf5ff; }
.bg-purple-500 { background-color: #a855f7; }
.bg-purple-600 { background-color: #9333ea; }

.bg-indigo-100 { background-color: #e0e7ff; }
.bg-indigo-600 { background-color: #4f46e5; }
