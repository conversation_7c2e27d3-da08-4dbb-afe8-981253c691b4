<!-- Error Page -->

<section class="py-20 bg-white min-h-screen flex items-center">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <!-- Error Icon -->
        <div class="w-32 h-32 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-8">
            <i class="fas fa-exclamation-triangle text-red-600 text-4xl"></i>
        </div>

        <!-- Error Message -->
        <h1 class="text-4xl font-bold text-gray-900 mb-6">Oops! Something went wrong</h1>

        <p class="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
            We encountered an unexpected error while processing your request.
            Our team has been notified and is working to fix the issue.
        </p>

        <!-- Error Details (if available) -->
        <% if (typeof error !== 'undefined' && error) { %>
            <div class="bg-red-50 border border-red-200 rounded-lg p-6 mb-8 max-w-2xl mx-auto">
                <h2 class="text-lg font-semibold text-red-900 mb-2">Error Details:</h2>
                <p class="text-red-800 text-left">
                    <%= typeof error === 'string' ? error : error.message || 'An unknown error occurred' %>
                </p>

                <% if (process.env.NODE_ENV === 'development' && error.stack) { %>
                    <details class="mt-4">
                        <summary class="cursor-pointer text-red-700 font-medium">Stack Trace (Development)</summary>
                        <pre class="mt-2 text-xs text-red-600 bg-red-100 p-3 rounded overflow-auto text-left"><%= error.stack %></pre>
                    </details>
                <% } %>
            </div>
        <% } %>

        <!-- What to do next -->
        <div class="bg-gray-50 rounded-lg p-8 mb-8">
            <h2 class="text-2xl font-semibold text-gray-900 mb-4">What can you do?</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 text-left">
                <div class="flex items-start">
                    <div class="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center mr-3 mt-1 flex-shrink-0">
                        <i class="fas fa-redo text-sm"></i>
                    </div>
                    <div>
                        <h3 class="font-semibold text-gray-900 mb-1">Try Again</h3>
                        <p class="text-gray-600 text-sm">Refresh the page or try your action again in a few moments.</p>
                    </div>
                </div>

                <div class="flex items-start">
                    <div class="w-8 h-8 bg-green-600 text-white rounded-full flex items-center justify-center mr-3 mt-1 flex-shrink-0">
                        <i class="fas fa-home text-sm"></i>
                    </div>
                    <div>
                        <h3 class="font-semibold text-gray-900 mb-1">Go Home</h3>
                        <p class="text-gray-600 text-sm">Return to our homepage and navigate from there.</p>
                    </div>
                </div>

                <div class="flex items-start">
                    <div class="w-8 h-8 bg-purple-600 text-white rounded-full flex items-center justify-center mr-3 mt-1 flex-shrink-0">
                        <i class="fas fa-envelope text-sm"></i>
                    </div>
                    <div>
                        <h3 class="font-semibold text-gray-900 mb-1">Contact Us</h3>
                        <p class="text-gray-600 text-sm">If the problem persists, let us know and we'll help you out.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex flex-col sm:flex-row gap-4 justify-center mb-8">
            <button onclick="window.location.reload()" class="btn btn-primary btn-lg">
                <i class="fas fa-redo mr-2"></i>Try Again
            </button>
            <a href="/" class="btn btn-secondary btn-lg">
                <i class="fas fa-home mr-2"></i>Go Home
            </a>
            <a href="/contact" class="btn btn-outline btn-lg">
                <i class="fas fa-envelope mr-2"></i>Contact Support
            </a>
        </div>

        <!-- Quick Links -->
        <div class="bg-blue-50 rounded-lg p-6 max-w-2xl mx-auto">
            <h3 class="text-lg font-semibold text-blue-900 mb-4">
                <i class="fas fa-compass mr-2"></i>Quick Navigation
            </h3>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <a href="/projects" class="text-blue-700 hover:text-blue-900 text-sm">
                    <i class="fas fa-flask mr-1"></i>Projects
                </a>
                <a href="/blog" class="text-blue-700 hover:text-blue-900 text-sm">
                    <i class="fas fa-newspaper mr-1"></i>Blog
                </a>
                <a href="/resources" class="text-blue-700 hover:text-blue-900 text-sm">
                    <i class="fas fa-book mr-1"></i>Resources
                </a>
                <a href="/events" class="text-blue-700 hover:text-blue-900 text-sm">
                    <i class="fas fa-calendar mr-1"></i>Events
                </a>
            </div>
        </div>

        <!-- Error ID (for support) -->
        <div class="mt-8 pt-6 border-t border-gray-200">
            <p class="text-sm text-gray-500">
                Error ID: <%= Date.now().toString(36) %>-<%= Math.random().toString(36).substr(2, 9) %>
                <br>
                Time: <%= new Date().toISOString() %>
            </p>
        </div>
    </div>
</section>
