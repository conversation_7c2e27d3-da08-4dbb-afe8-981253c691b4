<!-- Admin Projects Index -->

<div class="admin-header">
    <div class="flex items-center justify-between">
        <h1 class="admin-title">
            <i class="fas fa-flask mr-2 text-blue-600"></i>Projects Management
        </h1>
        <a href="/admin/projects/create" class="btn btn-primary">
            <i class="fas fa-plus mr-2"></i>Add New Project
        </a>
    </div>
</div>

<!-- Stats Cards -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
    <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                <i class="fas fa-flask text-blue-600"></i>
            </div>
            <div>
                <h3 class="text-2xl font-bold text-gray-900"><%= projects.length %></h3>
                <p class="text-gray-600">Total Projects</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mr-4">
                <i class="fas fa-star text-yellow-600"></i>
            </div>
            <div>
                <h3 class="text-2xl font-bold text-gray-900"><%= projects.filter(p => p.featured).length %></h3>
                <p class="text-gray-600">Featured</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-4">
                <i class="fas fa-file-pdf text-green-600"></i>
            </div>
            <div>
                <h3 class="text-2xl font-bold text-gray-900"><%= projects.filter(p => p.pdfFile).length %></h3>
                <p class="text-gray-600">With PDFs</p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mr-4">
                <i class="fas fa-calendar text-purple-600"></i>
            </div>
            <div>
                <h3 class="text-2xl font-bold text-gray-900">
                    <%= projects.filter(p => new Date(p.createdAt) > new Date(Date.now() - 30*24*60*60*1000)).length %>
                </h3>
                <p class="text-gray-600">This Month</p>
            </div>
        </div>
    </div>
</div>

<!-- Search and Filter -->
<div class="bg-white rounded-lg shadow-md p-6 mb-8">
    <div class="flex flex-col md:flex-row gap-4">
        <div class="flex-1">
            <input
                type="text"
                id="search-projects"
                placeholder="Search projects..."
                class="form-input w-full"
            >
        </div>
        <div class="flex gap-4">
            <select id="filter-featured" class="form-select">
                <option value="">All Projects</option>
                <option value="featured">Featured Only</option>
                <option value="not-featured">Not Featured</option>
            </select>
            <select id="filter-pdf" class="form-select">
                <option value="">All Projects</option>
                <option value="with-pdf">With PDF</option>
                <option value="without-pdf">Without PDF</option>
            </select>
        </div>
    </div>
</div>

<!-- Projects Table -->
<div class="bg-white rounded-lg shadow-md overflow-hidden">
    <% if (projects && projects.length > 0) { %>
        <table class="table" data-sortable>
            <thead>
                <tr>
                    <th class="w-8">
                        <input type="checkbox" id="select-all" class="form-checkbox">
                    </th>
                    <th data-sort="title">Title</th>
                    <th data-sort="authors">Authors</th>
                    <th data-sort="featured">Status</th>
                    <th data-sort="created">Created</th>
                    <th class="w-32">Actions</th>
                </tr>
            </thead>
            <tbody id="projects-tbody">
                <% projects.forEach(project => { %>
                    <tr class="project-row" data-project-id="<%= project._id %>">
                        <td>
                            <input type="checkbox" name="selected_items[]" value="<%= project._id %>" class="form-checkbox">
                        </td>
                        <td data-title="<%= project.title %>">
                            <div class="flex items-center">
                                <% if (project.image) { %>
                                    <img src="<%= project.image %>" alt="<%= project.title %>" class="w-12 h-12 object-cover rounded-lg mr-3">
                                <% } else { %>
                                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-flask text-blue-600"></i>
                                    </div>
                                <% } %>
                                <div>
                                    <h3 class="font-medium text-gray-900"><%= project.title %></h3>
                                    <p class="text-sm text-gray-500">
                                        <%= project.description.length > 60 ? project.description.substring(0, 60) + '...' : project.description %>
                                    </p>
                                </div>
                            </div>
                        </td>
                        <td data-authors="<%= project.authors ? project.authors.join(', ') : '' %>">
                            <% if (project.authors && project.authors.length > 0) { %>
                                <div class="text-sm">
                                    <% project.authors.slice(0, 2).forEach(author => { %>
                                        <span class="block text-gray-900"><%= author %></span>
                                    <% }) %>
                                    <% if (project.authors.length > 2) { %>
                                        <span class="text-gray-500">+<%= project.authors.length - 2 %> more</span>
                                    <% } %>
                                </div>
                            <% } else { %>
                                <span class="text-gray-400">No authors</span>
                            <% } %>
                        </td>
                        <td data-featured="<%= project.featured ? 'featured' : 'not-featured' %>">
                            <div class="flex flex-col gap-1">
                                <% if (project.featured) { %>
                                    <span class="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded-full">
                                        <i class="fas fa-star mr-1"></i>Featured
                                    </span>
                                <% } %>
                                <% if (project.pdfFile) { %>
                                    <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                                        <i class="fas fa-file-pdf mr-1"></i>PDF
                                    </span>
                                <% } %>
                                <% if (project.tags && project.tags.length > 0) { %>
                                    <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                                        <%= project.tags.length %> tags
                                    </span>
                                <% } %>
                            </div>
                        </td>
                        <td data-created="<%= project.createdAt %>">
                            <div class="text-sm">
                                <div class="text-gray-900"><%= new Date(project.createdAt).toLocaleDateString() %></div>
                                <div class="text-gray-500"><%= new Date(project.createdAt).toLocaleTimeString() %></div>
                            </div>
                        </td>
                        <td>
                            <div class="flex items-center space-x-2">
                                <a href="/projects/<%= project._id %>" target="_blank" class="text-blue-600 hover:text-blue-800" title="View">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="/admin/projects/<%= project._id %>/edit" class="text-green-600 hover:text-green-800" title="Edit">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <form action="/admin/projects/<%= project._id %>?_method=DELETE" method="POST" class="inline">
                                    <button type="submit" class="text-red-600 hover:text-red-800" title="Delete"
                                            data-confirm="Are you sure you want to delete this project?">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                <% }) %>
            </tbody>
        </table>

        <!-- Bulk Actions -->
        <div id="bulk-actions" class="hidden p-4 bg-gray-50 border-t border-gray-200">
            <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600">
                    <span id="selected-count">0</span> projects selected
                </span>
                <div class="flex space-x-2">
                    <button class="btn btn-secondary" onclick="toggleFeatured()">
                        <i class="fas fa-star mr-1"></i>Toggle Featured
                    </button>
                    <button class="btn btn-danger" onclick="deleteSelected()" data-confirm="Are you sure you want to delete the selected projects?">
                        <i class="fas fa-trash mr-1"></i>Delete Selected
                    </button>
                </div>
            </div>
        </div>
    <% } else { %>
        <div class="p-12 text-center">
            <div class="w-24 h-24 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-flask text-gray-400 text-2xl"></i>
            </div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">No Projects Found</h3>
            <p class="text-gray-600 mb-6">Get started by creating your first research project.</p>
            <a href="/admin/projects/create" class="btn btn-primary">
                <i class="fas fa-plus mr-2"></i>Create First Project
            </a>
        </div>
    <% } %>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('search-projects');
    const featuredFilter = document.getElementById('filter-featured');
    const pdfFilter = document.getElementById('filter-pdf');
    const tbody = document.getElementById('projects-tbody');
    const rows = Array.from(document.querySelectorAll('.project-row'));
    const selectAllCheckbox = document.getElementById('select-all');
    const bulkActions = document.getElementById('bulk-actions');
    const selectedCount = document.getElementById('selected-count');

    // Search and filter functionality
    function filterProjects() {
        const searchTerm = searchInput.value.toLowerCase();
        const featuredValue = featuredFilter.value;
        const pdfValue = pdfFilter.value;

        rows.forEach(row => {
            const title = row.querySelector('[data-title]').getAttribute('data-title').toLowerCase();
            const authors = row.querySelector('[data-authors]').getAttribute('data-authors').toLowerCase();
            const featured = row.querySelector('[data-featured]').getAttribute('data-featured');
            const hasPdf = row.querySelector('.fa-file-pdf') !== null;

            const matchesSearch = title.includes(searchTerm) || authors.includes(searchTerm);
            const matchesFeatured = !featuredValue ||
                (featuredValue === 'featured' && featured === 'featured') ||
                (featuredValue === 'not-featured' && featured === 'not-featured');
            const matchesPdf = !pdfValue ||
                (pdfValue === 'with-pdf' && hasPdf) ||
                (pdfValue === 'without-pdf' && !hasPdf);

            row.style.display = matchesSearch && matchesFeatured && matchesPdf ? '' : 'none';
        });
    }

    // Bulk selection functionality
    function updateBulkActions() {
        const checkboxes = document.querySelectorAll('input[name="selected_items[]"]');
        const checkedBoxes = document.querySelectorAll('input[name="selected_items[]"]:checked');

        selectedCount.textContent = checkedBoxes.length;
        bulkActions.style.display = checkedBoxes.length > 0 ? 'block' : 'none';

        selectAllCheckbox.checked = checkboxes.length > 0 && checkedBoxes.length === checkboxes.length;
        selectAllCheckbox.indeterminate = checkedBoxes.length > 0 && checkedBoxes.length < checkboxes.length;
    }

    // Event listeners
    searchInput.addEventListener('input', filterProjects);
    featuredFilter.addEventListener('change', filterProjects);
    pdfFilter.addEventListener('change', filterProjects);

    selectAllCheckbox.addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('input[name="selected_items[]"]');
        checkboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updateBulkActions();
    });

    document.addEventListener('change', function(e) {
        if (e.target.name === 'selected_items[]') {
            updateBulkActions();
        }
    });

    // Initialize
    updateBulkActions();
});

function toggleFeatured() {
    const selectedIds = Array.from(document.querySelectorAll('input[name="selected_items[]"]:checked'))
        .map(cb => cb.value);

    if (selectedIds.length === 0) return;

    // Implementation would make AJAX calls to toggle featured status
    console.log('Toggle featured for:', selectedIds);
}

function deleteSelected() {
    const selectedIds = Array.from(document.querySelectorAll('input[name="selected_items[]"]:checked'))
        .map(cb => cb.value);

    if (selectedIds.length === 0) return;

    if (confirm(`Are you sure you want to delete ${selectedIds.length} projects?`)) {
        // Implementation would make AJAX calls to delete projects
        console.log('Delete projects:', selectedIds);
    }
}
</script>
